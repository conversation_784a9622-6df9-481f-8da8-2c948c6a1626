'ui';try{aladin={},map=new Map,dw=device.width,dh=device.height,dw121=Math.floor(dw/12*1),dw125=Math.floor(dw/12*5),dw127=Math.floor(dw/12*7),dw1211=Math.floor(dw/12*11),dh121=Math.floor(dh/12*1),dh125=Math.floor(dh/12*5),dh127=Math.floor(dh/12*7),dh1211=Math.floor(dh/12*11),dw111=Math.floor(dw/11*1),dw112=Math.floor(dw/11*2),dw113=Math.floor(dw/11*3),dw114=Math.floor(dw/11*4),dw115=Math.floor(dw/11*5),dw116=Math.floor(dw/11*6),dw117=Math.floor(dw/11*7),dw118=Math.floor(dw/11*8),dw119=Math.floor(dw/11*9),dw1110=Math.floor(dw/11*10),dh111=Math.floor(dh/11*1),dh112=Math.floor(dh/11*2),dh113=Math.floor(dh/11*3),dh114=Math.floor(dh/11*4),dh115=Math.floor(dh/11*5),dh116=Math.floor(dh/11*6),dh117=Math.floor(dh/11*7),dh118=Math.floor(dh/11*8),dh119=Math.floor(dh/11*9),dh1110=Math.floor(dh/11*10),dw101=Math.floor(.1*dw),dw103=Math.floor(.3*dw),dw107=Math.floor(.7*dw),dw109=Math.floor(.9*dw),dh101=Math.floor(.1*dh),dh103=Math.floor(.3*dh),dh107=Math.floor(.7*dh),dh109=Math.floor(.9*dh),dw91=Math.floor(dw/9*1),dw92=Math.floor(dw/9*2),dw94=Math.floor(dw/9*4),dw95=Math.floor(dw/9*5),dw97=Math.floor(dw/9*7),dw98=Math.floor(dw/9*8),dh91=Math.floor(dh/9*1),dh92=Math.floor(dh/9*2),dh94=Math.floor(dh/9*4),dh95=Math.floor(dh/9*5),dh97=Math.floor(dh/9*7),dh98=Math.floor(dh/9*8),dw81=Math.floor(dw/8*1),dw83=Math.floor(dw/8*3),dw85=Math.floor(dw/8*5),dw87=Math.floor(dw/8*7),dh81=Math.floor(dh/8*1),dh83=Math.floor(dh/8*3),dh85=Math.floor(dh/8*5),dh87=Math.floor(dh/8*7),dw71=Math.floor(dw/7*1),dw72=Math.floor(dw/7*2),dw73=Math.floor(dw/7*3),dw74=Math.floor(dw/7*4),dw75=Math.floor(dw/7*5),dw76=Math.floor(dw/7*6),dh71=Math.floor(dh/7*1),dh72=Math.floor(dh/7*2),dh73=Math.floor(dh/7*3),dh74=Math.floor(dh/7*4),dh75=Math.floor(dh/7*5),dh76=Math.floor(dh/7*6),dw61=Math.floor(dw/6*1),dw65=Math.floor(dw/6*5),dh61=Math.floor(dh/6*1),dh65=Math.floor(dh/6*5),dw51=Math.floor(dw/5*1),dw52=Math.floor(dw/5*2),dw53=Math.floor(dw/5*3),dw54=Math.floor(dw/5*4),dh51=Math.floor(dh/5*1),dh52=Math.floor(dh/5*2),dh53=Math.floor(dh/5*3),dh54=Math.floor(dh/5*4),dw41=Math.floor(dw/4*1),dw43=Math.floor(dw/4*3),dh41=Math.floor(dh/4*1),dh43=Math.floor(dh/4*3),dw31=Math.floor(dw/3*1),dw32=Math.floor(dw/3*2),dh31=Math.floor(dh/3*1),dh32=Math.floor(dh/3*2),dw21=Math.floor(dw/2*1),dh21=Math.floor(dh/2*1),uid=i(device.getAndroidId()),storage=storages.create(uid)}catch(t){}try{aladin={},xfcstatus=!0,xfcOnoff=!1,prstr="ppmt",prnum="1200",uid=i(device.getAndroidId()),prkey="k"+i(prstr+prnum),ifurl=["aHR0cDovL3R5LmF1dG9qcy5yZW4v","90f832a2104","0","1","3","2"],bsurl=(t=>{try{return new java.lang.String(java.util.Base64.getDecoder().decode(t))}catch(t){}})(ifurl[0]),uks=storage.get("uks",""),skeys=storage.get("skeys",""),scriptStatus=!1}catch(t){}try{ui.layout('\t\t\t<drawer id="drawer">\t\t\t\t<vertical>\t\t\t\t\t<appbar id="appbar" w="*">\t\t\t\t\t\t<toolbar id="toolbar" w="auto" layout_gravity="center" gravity="center" paddingLeft="50" paddingRight="50" marginTop="4" title="PPMT助手" />\t\t\t\t\t</appbar>\t\t\t\t\t<horizontal marginTop="0" w="auto" layout_gravity="center" gravity="center">\t\t\t\t\t\t<Switch id="wzaService" text="无障碍权限" padding="10 10 10 10" textSize="15sp" />\t\t\t\t\t\t<Switch id="xfcService" text="悬浮窗权限"  padding="10 10 10 10" textSize="15sp"/>\t\t\t\t\t</horizontal>\t\t\t\t\t<viewpager id="viewpager">\t\t\t\t\t\t<frame>\t\t\t\t\t\t\t<ScrollView>\t\t\t\t\t\t\t\t<vertical padding="10 0 10 0" w="*">\t\t\t\t\t\t\t\t\t<card w="*" h="auto" margin="10 5" cardCornerRadius="4dp" cardElevation="1dp" foreground="?selectableItemBackground">\t\t\t\t\t\t\t\t\t\t<horizontal gravity="center_vertical">\t\t\t\t\t\t\t\t\t\t\t<vertical padding="15 8" h="auto" w="auto" layout_weight="1">\t\t\t\t\t\t\t\t\t\t\t\t<text id="help" text="" textColor="#222222" textSize="16sp" maxLines="50" />\t\t\t\t\t\t\t\t\t\t\t</vertical>\t\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t</card>\t\t\t\t\t\t\t\t\t<horizontal marginTop="4" w="auto" layout_gravity="center" gravity="center">\t\t\t\t\t\t\t\t\t\t\t<checkbox id="endbox" text="是否端盒"/>\t\t\t\t\t\t\t\t\t\t\t<checkbox id="numadd" text="是否x2"/>\t\t\t\t\t\t\t\t\t\t\t<checkbox id="orderx" text="是否订单加速"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<radiogroup id="radiogroup" paddingLeft="10" paddingRight="10" marginTop="6" orientation="horizontal" layout_weight="1">\t\t\t\t\t\t\t\t\t\t<radio id="radio1" text="安全模式" layout_weight="1"/>\t\t\t\t\t\t\t\t\t\t<radio id="radio2" text="暴力模式" layout_weight="1"/>\t\t\t\t\t\t\t\t\t\t<radio id="radio3" text="疯狂模式" layout_weight="1"/>\t\t\t\t\t\t\t\t\t</radiogroup>\t\t\t\t\t\t\t\t\t\x3c!--内容不够|图片填充--\x3e\t\t\t\t\t\t\t\t\t\x3c!--<card w="match_parent" h="wrap_content" cardCornerRadius="4dp" cardElevation="1dp">\t\t\t\t\t\t\t\t\t\t<img src="https://pic1.imgdb.cn/item/681b8d4058cb8da5c8e3e204.png" w="match_parent" h="wrap_content" scaleType="fitXY" />\t\t\t\t\t\t\t\t\t</card>--\x3e\t\t\t\t\t\t\t\t\t<horizontal paddingLeft="10"  paddingRight="0"  marginTop="20">\t\t\t\t\t\t\t\t\t\t<text w="auto" h="auto" text="刷新间隔(毫秒)："/>\t\t\t\t\t\t\t\t\t\t<text id="valueText" text="580" gravity="center"/>\t\t\t\t\t\t\t\t\t\t<seekbar id="slider" layout_weight="10" min="100" max="1500" progress="580"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<horizontal paddingLeft="10"  paddingRight="0"  marginTop="20">\t\t\t\t\t\t\t\t\t\t<text w="auto" h="auto" text="刷新等待(毫秒)："/>\t\t\t\t\t\t\t\t\t\t<text id="valueText1" text="660" gravity="center"/>\t\t\t\t\t\t\t\t\t\t<seekbar id="slider1" layout_weight="10" min="100" max="1500" progress="660"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<horizontal paddingLeft="10"  paddingRight="0"  marginTop="20">\t\t\t\t\t\t\t\t\t\t<text w="auto" h="auto" text="提交延迟(毫秒)："/>\t\t\t\t\t\t\t\t\t\t<text id="valueText2" text="150" gravity="center"/>\t\t\t\t\t\t\t\t\t\t<seekbar id="slider2" layout_weight="10" min="10" max="5000" progress="150"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<horizontal paddingLeft="10"  paddingRight="0"  marginTop="20">\t\t\t\t\t\t\t\t\t\t<text w="auto" h="auto" text="订单加速(毫秒)："/>\t\t\t\t\t\t\t\t\t\t<text id="valueText3" text="280" gravity="center"/>\t\t\t\t\t\t\t\t\t\t<seekbar id="slider3" layout_weight="10" min="50" max="350" progress="280"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<text text="根据运行状态调节上述值可达最佳抢购状态" textSize="14sp" gravity="center"   marginTop="10"/>\t\t\t\t\t\t\t\t\t<text text="必须关闭导航键改成全屏手势" textSize="14sp" gravity="center"   marginTop="10"/>\t\t\t\t\t\t\t\t\t<horizontal marginTop="15" w="auto" layout_gravity="center">\t\t\t\t\t\t\t\t\t\t<button id="reSet" margin="6"  text="重置" />\t\t\t\t\t\t\t\t\t\t<button id="mSave" margin="6"  text="保存" />\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<horizontal marginTop="15" w="auto" layout_gravity="center">\t\t\t\t\t\t\t\t\t\t<button id="bgXfc" margin="6"  style="Widget.AppCompat.Button.Colored" text="悬浮窗" />\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<horizontal marginTop="6" w="auto" layout_gravity="center">\t\t\t\t\t\t\t\t\t\t<button id="activation" margin="6" textColor="red" text="激活"/>\t\t\t\t\t\t\t\t\t</horizontal>\t\t\t\t\t\t\t\t\t<text id="time" margin="0 100 0 5" text="✨ 泡泡玛特智能抢购助手" textSize="16sp" gravity="center"/>\t\t\t\t\t\t\t\t</vertical>\t\t\t\t\t\t\t</ScrollView>\t\t\t\t\t\t</frame>\t\t\t\t\t</viewpager>\t\t\t\t</vertical>\t\t\t</drawer>'),color="#009688",ui.statusBarColor(color),ui.appbar.attr("bg",color);ui.help.setText('🔉. 点击"悬浮窗"可打开或关闭悬浮窗\n💬. 悬浮窗①②③④⑤号按钮功能如下\n①号按钮功能为："送到家"与"到店取"切换刷新加载，调试"刷新等待"的值\n②号按钮功能为："送到家"重复刷新加载，调试"刷新间隔"的值\n③号按钮功能为："到店取"重复刷新加载，调试"刷新间隔"的值\n④号按钮功能为：结束当前脚本运行\n⑤号按钮功能为：打开当前页面'),ui.endbox.on("check",t=>{t?storage.put("endbox",1):storage.put("endbox",-1)}),ui.numadd.on("check",t=>{t?storage.put("numadd",1):storage.put("numadd",-1)}),ui.orderx.on("check",t=>{t?storage.put("orderx",1):storage.put("orderx",-1)}),ui.endbox.attr("checked",0<storage.get("endbox",0)),ui.numadd.attr("checked",0<storage.get("numadd",0)),ui.orderx.attr("checked",0<storage.get("orderx",0));var t=parseInt(storage.get("radiogroup",1))||1,e=(1==t?ui.radiogroup.check(ui.radio1.getId()):2==t?ui.radiogroup.check(ui.radio2.getId()):ui.radiogroup.check(ui.radio3.getId()),ui.radio1.on("check",t=>{t&&storage.put("radiogroup",1)}),ui.radio2.on("check",t=>{t&&storage.put("radiogroup",2)}),ui.radio3.on("check",t=>{t&&storage.put("radiogroup",3)}),ui.activation.click(function(){rawInput("请输入激活码","",t=>{switch(((c,n)=>{if(!n)return 0;storage.put("preks",n);var t=(new Date).getTime(),e=i(prkey+t),r=bsurl+ifurl[1]+"?uid="+c+"&uks="+n+"&s="+prstr+"&k="+prnum+"&dtnum=31&t="+t;http.get(r,{headers:{uid:c,t:t,scr:e}},function(t,e){var r,a,d,o;return t&&200==t.statusCode&&74<=(skeys=t.body.string()).length?(toast("激活成功"),storage.put("skeys",skeys),t=skeys.substring(0,32),r=skeys.substring(32,skeys.length-32),a=skeys.substring(skeys.length-32),d=1e5*Number(r)/903-1e4,o=h(new Date(parseInt(d)),"yyyyMMddhhmmss"),t===i(c+d)&&a===i(i(prstr+"100000000:00"+i(c+d)+prstr+"100000000:00")+prstr+"100000000:00"+r+i(c)+prnum)?l(o)?(ui.run(function(){ui.time.setText(" 到期时间: "+h(new Date(parseInt(d)),"yyyy年MM月dd日 hh:mm:ss")),ui.activation.attr("visibility","gone"),ui.time.attr("color","#80000000")}),uks=n,storage.put("uks",n),storage.put(prkey,prstr+prnum),1):(ui.run(function(){ui.time.setText(" 时间过期: "+h(new Date(parseInt(d)),"yyyy年MM月dd日 hh:mm:ss"))}),2):0):(toast("激活失败"),0)})})(uid,t)){case 0:toast("激活失败");break;case 1:scriptStatus=!0,toast("激活成功");break;case 2:toast("激活码时间过期")}})}),parseInt(storage.get("slider",580))||580),r=(ui.slider.setProgress(e),ui.valueText.setText(""+e),ui.slider.setOnSeekBarChangeListener({onProgressChanged:function(t,e,r){ui.valueText.setText(""+e),storage.put("slider",""+e)}}),parseInt(storage.get("slider1",660))||660),a=(ui.slider1.setProgress(r),ui.valueText1.setText(""+r),ui.slider1.setOnSeekBarChangeListener({onProgressChanged:function(t,e,r){ui.valueText1.setText(""+e),storage.put("slider1",""+e)}}),parseInt(storage.get("slider2",150))||150),d=(ui.slider2.setProgress(a),ui.valueText2.setText(""+a),ui.slider2.setOnSeekBarChangeListener({onProgressChanged:function(t,e,r){ui.valueText2.setText(""+e),storage.put("slider2",""+e)}}),parseInt(storage.get("slider3",280))||280);ui.slider3.setProgress(d),ui.valueText3.setText(""+d),ui.slider3.setOnSeekBarChangeListener({onProgressChanged:function(t,e,r){ui.valueText3.setText(""+e),storage.put("slider3",""+e)}}),ui.reSet.on("click",t=>{try{ui.slider.setProgress(580),ui.slider1.setProgress(660),ui.slider2.setProgress(150),ui.slider3.setProgress(280),storage.put("slider","580"),storage.put("slider1","660"),storage.put("slider2","150"),storage.put("slider3","280"),ui.endbox.attr("checked",!1),ui.numadd.attr("checked",!1),ui.orderx.attr("checked",!1),toast("重置成功")}catch(t){}}),ui.mSave.on("click",t=>{try{toast("保存成功")}catch(t){}}),ui.bgXfc.on("click",t=>{try{xfcOnoff?(xfcOnoff=!1,n.hide()):(xfcOnoff=!0,n.show())}catch(t){}}),setInterval(function(){try{auto.service?ui.wzaService.attr("checked",!0):ui.wzaService.attr("checked",!1),xfcstatus&&(floaty.checkPermission()?ui.xfcService.attr("checked",!0):ui.xfcService.attr("checked",!1))}catch(t){}},1e3);ui.wzaService.on("click",t=>{try{auto.service?auto.service.disableSelf():app.startActivity({action:"android.settings.ACCESSIBILITY_SETTINGS"})}catch(t){}}),ui.xfcService.on("click",t=>{try{xfcstatus=!1,floaty.checkPermission()||floaty.requestPermission()}catch(t){}});try{(()=>{try{s()&&ui.run(function(){ui.activation.attr("visibility","gone"),ui.time.attr("color","#80000000")})}catch(t){}})(ui)}catch(t){}}catch(t){}function i(t){try{for(var e=java.math.BigInteger(1,java.security.MessageDigest.getInstance("MD5").digest(java.lang.String(t).getBytes())).toString(16);e.length<32;)e="0"+e;return e}catch(t){}}function y(t){try{return void(t&&((t,e,r)=>{try{var a=(new Date).getTime();if(aladin.hasOwnProperty(t)){if(a-aladin[t]<e)return;0<r&&(aladin[t]=a)}else aladin[t]=a}catch(t){}return 1})(t.text().trim(),120,1)&&t.click())}catch(t){}}let{FloatButton:o,FloatButtonConfig:c}=require("./FloatButton/init"),n=new o;function s(){var t,e,r,a,d,o;if(storage.get("skeys",""))switch(t=uid,(e=storage.get("skeys",""))&&74<=e.length&&(r=e.substring(0,32),a=e.substring(32,e.length-32),e=e.substring(e.length-32),d=1e5*Number(a)/903-1e4,o=h(new Date(parseInt(d)),"yyyyMMddhhmmss"),r===i(t+d))&&e===i(i(prstr+"100000000:00"+i(t+d)+prstr+"100000000:00")+prstr+"100000000:00"+a+i(t)+prnum)?l(o)?(ui.run(function(){ui.time.setText(" 到期时间: "+h(new Date(parseInt(d)),"yyyy年MM月dd日 hh:mm:ss")),ui.activation.attr("visibility","gone"),ui.time.attr("color","#80000000")}),1):(ui.run(function(){ui.time.setText(" 时间过期: "+h(new Date(parseInt(d)),"yyyy年MM月dd日 hh:mm:ss"))}),2):0){case 0:scriptStatus=!1;break;case 1:scriptStatus=!0;break;case 2:scriptStatus=!1}return scriptStatus}function h(t,e){var r,a={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};for(r in/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))),a)new RegExp("("+r+")").test(e)&&(e=e.replace(RegExp.$1,1==RegExp.$1.length?a[r]:("00"+a[r]).substr((""+a[r]).length)));return e}function l(t){return new java.text.SimpleDateFormat("yyyyMMddHHmmss").format(new Date)<t||(()=>{try{var t=http.get("http://quan.suning.com/getSysTime.do");if(t)return t.body.json().sysTime1}catch(t){}})()<t}c.time.direction=520,n.on("create",function(){n.setIcon("file://./res/jqr2.png"),n.setColor("#FFFFFF"),n.setAllButtonSize(41),n.addItem("按钮1").toCheckbox(t=>{t.icon1("@drawable/ic_play_arrow_black_48dp").tint1("#FFFFFF").color1("#41A4F5"),t.icon2("@drawable/ic_stop_black_48dp").tint2("#FFFFFF").color2("#ED524E")}).onClick((t,e,r)=>{try{if(!s())return toast("未激活"),!1;if(1==r){parseInt(storage.get("slider",580));let u=parseInt(storage.get("slider1",660))||660,e=parseInt(storage.get("slider2",150))||150,f=parseInt(storage.get("slider3",280))||280;var d=0<storage.get("endbox",0),o=0<storage.get("numadd",0);let g=0<storage.get("orderx",0);var c=parseInt(storage.get("radiogroup",1))||1;let r=!1,a=!1,w=(d||(a=!0),!1),p=(o||(w=!0),0);let x=0;var n;1==c?((n=textMatches(".*?立即购买.*?").findOnce())&&(y(n),p=1),threads.start(function(){try{for(;;)try{var t,e,r,a,d,o,c,n;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-x&&(x=e,!w&&(w=!0,r=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(a=r.bounds().left,d=r.bounds().centerY(),o=dw-a-random(30,50),click(o,d)),t.click(),g)&&(sleep(f),g=!1,c=dw-random(150,250),n=dh-random(60,100),click(c,n))}catch(t){}}catch(t){}}),threads.start(function(){try{for(var t,e,r,a,d,o,c,n,i,s,h,l=!1;;){try{0==p?(2!=p&&(p=1),(t=textMatches(".*?立即购买.*?").findOnce())&&y(t)):1==p&&((e=text("确定").findOnce())?100<(r=(new Date).getTime())-x&&(x=r,!w&&(w=!0,a=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(d=a.bounds().left,o=a.bounds().centerY(),c=dw-d-random(30,50),click(c,o)),e.click(),g)&&(sleep(f),g=!1,n=dw-random(150,250),i=dh-random(60,100),click(n,i)):l?(l=!1,(s=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())?s.click():2!=p&&(p=0)):(l=!0,(h=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&h.click()))}catch(t){}sleep(u)}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t?(p=2,t.click(),r&&sleep(e),r=!0):p=1}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{1!=p||a||(t=textMatches("整盒.*?盒").findOnce())&&(y(t),a=!0);var t,e,r=textMatches("就是这家|我知道了|确认无误").findOnce();r?y(r):(e=textMatches("开售通知|确认以上信息并预定|到货通知").findOnce())&&y(e)}catch(t){}})):2==c?(threads.start(function(){try{for(;;)try{var t,e,r,a;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-x&&(x=e,t.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a))}catch(t){}}catch(t){}}),threads.start(function(){try{for(var t,e,r,a,d,o,c=textMatches(".*?立即购买.*?").findOnce(),n=(c&&c.click(),!1);;)try{sleep(u),text("确认订单").findOnce()||((t=text("确定").findOnce())?100<(e=(new Date).getTime())-x&&(x=e,t.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):n?(n=!1,(d=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())?d.click():(c=textMatches(".*?立即购买.*?").findOnce())&&c.click()):(n=!0,(o=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&o.click()))}catch(t){}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}})):(threads.start(function(){try{for(;;)try{var t,e,r,a=text("确定").findOnce();a&&100<(t=(new Date).getTime())-x&&(x=t,a.click(),g)&&(sleep(f),g=!1,e=dw-random(150,250),r=dh-random(60,100),click(e,r))}catch(t){}}catch(t){}}),threads.start(function(){try{(o=textMatches(".*?立即购买.*?").findOnce())&&o.click();for(var t=!1;;)try{sleep(u);var e,r,a,d,o,c,n=text("确定").findOnce();n?100<(e=(new Date).getTime())-x&&(x=e,n.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):t?(t=!1,(d=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())?d.click():(o=textMatches(".*?立即购买.*?").findOnce())&&o.click()):(t=!0,(c=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&c.click())}catch(t){}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}}))}else try{threads.shutDownAll()}catch(t){}}catch(t){}return!1}),n.addItem("按钮2").toCheckbox(t=>{t.icon1("@drawable/ic_looks_two_black_48dp").tint1("#FFFFFF").color1("#BFBFBF"),t.icon2("@drawable/ic_stop_black_48dp").tint2("#FFFFFF").color2("#ED524E")}).onClick((t,e,r)=>{try{if(!s())return!1;if(1==r){let l=parseInt(storage.get("slider",580))||580;parseInt(storage.get("slider1",660));let e=parseInt(storage.get("slider2",150))||150,u=parseInt(storage.get("slider3",280))||280;var d=0<storage.get("endbox",0),o=0<storage.get("numadd",0);let f=0<storage.get("orderx",0);var c=parseInt(storage.get("radiogroup",1))||1;let r=!1,a=!1,g=(d||(a=!0),!1),w=(o||(g=!0),0);let p=0;1==c?(threads.start(function(){try{for(;;)try{var t,e,r,a,d,o,c,n;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-p&&(p=e,!g&&(g=!0,r=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(a=r.bounds().left,d=r.bounds().centerY(),o=dw-a-random(30,50),click(o,d)),t.click(),f)&&(sleep(u),f=!1,c=dw-random(150,250),n=dh-random(60,100),click(c,n))}catch(t){}}catch(t){}}),threads.start(function(){try{for(var t,e,r,a,d,o,c,n,i,s=!1,h=null;;){try{0==w?(2!=w&&(w=1),(h=textMatches(".*?立即购买.*?").findOnce())&&y(h)):1==w&&((t=text("确定").findOnce())?100<(e=(new Date).getTime())-p&&(p=e,!g&&(g=!0,r=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(a=r.bounds().left,d=r.bounds().centerY(),o=dw-a-random(30,50),click(o,d)),t.click(),f)&&(sleep(u),f=!1,c=dw-random(150,250),n=dh-random(60,100),click(c,n)):s?(h=h||textMatches(".*?立即购买.*?").findOnce())&&y(h):(i=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&(s=!0,i.click()))}catch(t){}sleep(l)}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t?(w=2,t.click(),r&&sleep(e),r=!0):w=1}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{1!=w||a||(t=textMatches("整盒.*?盒").findOnce())&&y(t);var t,e,r=textMatches("我知道了|就是这家|确认无误").findOnce();r?y(r):(e=textMatches("开售通知|确认以上信息并预定|到货通知").findOnce())&&y(e)}catch(t){}})):2==c?(threads.start(function(){try{for(;;)try{var t,e,r,a;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-p&&(p=e,t.click(),f)&&(sleep(u),f=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a))}catch(t){}}catch(t){}}),threads.start(function(){try{for(var t,e,r,a,d,o=textMatches(".*?立即购买.*?").findOnce(),c=(o&&o.click(),!1);;)try{sleep(l),text("确认订单").findOnce()||((t=text("确定").findOnce())?100<(e=(new Date).getTime())-p&&(p=e,t.click(),f)&&(sleep(u),f=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):c?(o=o||textMatches(".*?立即购买.*?").findOnce())&&o.click():(d=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&(c=!0,d.click()))}catch(t){}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}})):(threads.start(function(){try{for(;;)try{var t,e,r,a=text("确定").findOnce();a&&100<(t=(new Date).getTime())-p&&(p=t,a.click(),f)&&(sleep(u),f=!1,e=dw-random(150,250),r=dh-random(60,100),click(e,r))}catch(t){}}catch(t){}}),threads.start(function(){try{(d=textMatches(".*?立即购买.*?").findOnce())&&d.click();for(var t=!1;;)try{sleep(l);var e,r,a,d,o,c=text("确定").findOnce();c?100<(e=(new Date).getTime())-p&&(p=e,c.click(),f)&&(sleep(u),f=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):t?(d=d||textMatches(".*?立即购买.*?").findOnce())&&d.click():(o=text("送到家").boundsInside(0,dh41,dw31,dh).findOnce())&&(t=!0,o.click())}catch(t){}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}}))}else try{threads.shutDownAll()}catch(t){}}catch(t){}return!1}),n.addItem("按钮3").toCheckbox(t=>{t.icon1("@drawable/ic_looks_3_black_48dp").tint1("#FFFFFF").color1("#019581"),t.icon2("@drawable/ic_stop_black_48dp").tint2("#FFFFFF").color2("#ED524E")}).onClick((t,e,r)=>{try{if(!s())return!1;if(1==r){let u=parseInt(storage.get("slider",580))||580;parseInt(storage.get("slider1",660));let e=parseInt(storage.get("slider2",150))||150,f=parseInt(storage.get("slider3",280))||280;var d=0<storage.get("endbox",0),o=0<storage.get("numadd",0);let g=0<storage.get("orderx",0);var c=parseInt(storage.get("radiogroup",1))||1;let r=!1,a=!1,w=(d||(a=!0),!1),p=(o||(w=!0),0);let x=0;1==c?(threads.start(function(){try{for(;;)try{var t,e,r,a,d,o,c,n;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-x&&(x=e,!w&&(w=!0,r=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(a=r.bounds().left,d=r.bounds().centerY(),o=dw-a-random(30,50),click(o,d)),t.click(),g)&&(sleep(f),g=!1,c=dw-random(150,250),n=dh-random(60,100),click(c,n))}catch(t){}}catch(t){}}),threads.start(function(){try{for(var t,e,r,a,d,o,c,n,i,s=!1,h=null;;){try{0==p?(2!=p&&(p=1),(h=textMatches(".*?立即购买.*?").findOnce())&&y(h)):1==p&&((t=text("确定").findOnce())?100<(e=(new Date).getTime())-x&&(x=e,!w&&(w=!0,r=text("数量").boundsInside(0,dh41,dw21,dh).findOnce())&&(a=r.bounds().left,d=r.bounds().centerY(),o=dw-a-random(30,50),click(o,d)),t.click(),g)&&(sleep(f),g=!1,c=dw-random(150,250),n=dh-random(60,100),click(c,n)):s?(h=h||textMatches(".*?立即购买.*?").findOnce())&&y(h):(i=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())&&(s=!0,i.click()))}catch(t){}var l=u-random(-50,50);sleep(l)}}catch(t){}}),threads.start(function(){try{for(;;)try{var t=text("确认信息并支付").findOnce();t?(p=2,t.click(),r&&sleep(e),r=!0):p=1}catch(t){}}catch(t){}}),threads.start(function(){for(;;)try{1!=p||a||(t=textMatches("整盒.*?盒").findOnce())&&y(t);var t,e,r=textMatches("我知道了|就是这家|确认无误").findOnce();r?y(r):(e=textMatches("开售通知|确认以上信息并预定|到货通知").findOnce())&&y(e)}catch(t){}})):2==c?(threads.start(function(){try{for(;;)try{var t,e,r,a;text("确认订单").findOnce()||(t=text("确定").findOnce())&&100<(e=(new Date).getTime())-x&&(x=e,t.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a))}catch(t){}}catch(t){}}),threads.start(function(){try{(d=textMatches(".*?立即购买.*?").findOnce())&&d.click();for(var t=!1;;){try{if(text("确认订单").findOnce())continue;var e,r,a,d,o,c=text("确定").findOnce();c?100<(e=(new Date).getTime())-x&&(x=e,c.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):t?(d=d||textMatches(".*?立即购买.*?").findOnce())&&d.click():(o=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())&&(t=!0,o.click())}catch(t){}var n=u-random(-50,50);sleep(n)}}catch(t){}}),threads.start(function(){for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}})):(threads.start(function(){try{for(;;)try{var t,e,r,a=text("确定").findOnce();a&&100<(t=(new Date).getTime())-x&&(x=t,a.click(),g)&&(sleep(f),g=!1,e=dw-random(150,250),r=dh-random(60,100),click(e,r))}catch(t){}}catch(t){}}),threads.start(function(){try{(d=textMatches(".*?立即购买.*?").findOnce())&&d.click();for(var t=!1;;){try{var e,r,a,d,o,c=text("确定").findOnce();c?100<(e=(new Date).getTime())-x&&(x=e,c.click(),g)&&(sleep(f),g=!1,r=dw-random(150,250),a=dh-random(60,100),click(r,a)):t?(d=d||textMatches(".*?立即购买.*?").findOnce())&&d.click():(o=text("到店取").boundsInside(dw51,dh41,dw53,dh).findOnce())&&(t=!0,o.click())}catch(t){}var n=u-random(-50,50);sleep(n)}}catch(t){}}),threads.start(function(){for(;;)try{var t=text("确认信息并支付").findOnce();t&&(t.click(),sleep(e))}catch(t){}}),threads.start(function(){for(;;)try{var t=textMatches("就是这家|我知道了|确认无误").findOnce();t&&y(t)}catch(t){}}))}else try{threads.shutDownAll()}catch(t){}}catch(t){}return!1}),n.addItem("按钮4").toCheckbox(t=>{t.icon1("@drawable/ic_clear_black_48dp").tint1("#FFFFFF").color1("#FCD633"),t.icon2("@drawable/ic_clear_black_48dp").tint2("#FFFFFF").color2("#FCD633")}).onClick((t,e,r)=>(exit(),!1)),n.addItem("按钮5").toCheckbox(t=>{t.icon1("@drawable/ic_format_list_bulleted_black_48dp").tint1("#FFFFFF").color1("#BFBFBF"),t.icon2("@drawable/ic_format_list_bulleted_black_48dp").tint2("#FFFFFF").color2("#BFBFBF")}).onClick((t,e,r)=>{try{launch("com.ppmtzs.scr")}catch(t){}return!1}),n.setAutoCloseMenuTime(3e3)}),n.on("item_click",(t,e,r)=>{}),n.on("show",()=>{}),n.on("hide",()=>{}),n.show(),xfcOnoff=!0;