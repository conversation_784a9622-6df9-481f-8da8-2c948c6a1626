{"abis": ["arm64-v8a", "armeabi-v7a"], "assets": [], "build": {"build_id": "D0899DD7-100", "build_number": 100, "build_time": 1750603548486, "release": true}, "encryptLevel": 3, "features": {"activityIntentTasks": false, "builtInOCR": "disabled", "nodejs": "disabled"}, "icon": "images/ic_app_logo.png", "launchConfig": {"displaySplash": false, "hideLogs": false, "splashIcon": "images/ic_splash.png", "splashLayoutXml": "splash.xml", "splashText": "PPMT助手", "stableMode": false}, "main": "main.js", "name": "PPMT助手", "optimization": {"obfuscateComponents": false, "removeAccessibilityService": false, "removeOpenCv": false, "unusedResources": false}, "packageName": "com.ppmtzs.scr", "permissionConfig": {"requestListOnStartup": ["android.permission.WRITE_EXTERNAL_STORAGE"]}, "plugins": {}, "scripts": {}, "signingConfig": {"alias": "aladin", "keystore": "/storage/emulated/0/脚本/.keystore/aladin.jks", "uuid": "33a56441-9439-4a28-ade5-16598f90cf68"}, "useFeatures": [], "versionCode": 100, "versionName": "1.00"}